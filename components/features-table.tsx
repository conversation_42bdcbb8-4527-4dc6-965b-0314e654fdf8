import { Check } from "lucide-react"
import { themes, type ThemeK<PERSON> } from "@/lib/themes"
import { cn } from "@/lib/utils"

interface Feature {
  name: string
  description: string
  vip: boolean
  premium: boolean
  ultraPremium: boolean
}

interface FeaturesTableProps {
  theme: ThemeKey
  selectedPlan: string
}

const features: Feature[] = [
  {
    name: "Unlimited Likes",
    description: "Like Freely Without Worry",
    vip: true,
    premium: true,
    ultraPremium: true,
  },
  {
    name: "5 Free Super Like per Day",
    description: "5x Match Success Rate",
    vip: true,
    premium: true,
    ultraPremium: true,
  },
]

export function FeaturesTable({ theme, selectedPlan }: FeaturesTableProps) {
  const themeConfig = themes[theme]

  const getColumnStyle = (planType: string) => {
    const isSelected = selectedPlan === planType
    return cn(
      "text-center font-semibold py-2 rounded-lg",
      isSelected ? [themeConfig.selectedPlan, themeConfig.selectedPlanText] : themeConfig.text,
    )
  }

  return (
    <div className={cn(themeConfig.cardBg, "rounded-2xl p-6 border", themeConfig.border)}>
      <div className="grid grid-cols-4 gap-4">
        <div className={cn("font-semibold", themeConfig.text)}>Features</div>
        <div className={getColumnStyle("VIP")}>VIP</div>
        <div className={getColumnStyle("Premium")}>Premium</div>
        <div className={getColumnStyle("Ultra Premium")}>Ultra Premium</div>

        {features.map((feature, index) => (
          <div key={index} className="col-span-4 grid grid-cols-4 gap-4 py-3 border-t border-gray-200/20">
            <div>
              <div className={cn("font-medium", themeConfig.text)}>{feature.name}</div>
              <div className={cn("text-sm", themeConfig.textSecondary)}>{feature.description}</div>
            </div>
            <div className="text-center">
              {feature.vip && <Check className={cn("w-5 h-5 mx-auto", themeConfig.accent)} />}
            </div>
            <div className="text-center">
              {feature.premium && <Check className={cn("w-5 h-5 mx-auto", themeConfig.accent)} />}
            </div>
            <div className="text-center">
              {feature.ultraPremium && <Check className={cn("w-5 h-5 mx-auto", themeConfig.accent)} />}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
