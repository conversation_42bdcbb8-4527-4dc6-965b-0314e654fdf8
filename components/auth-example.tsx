'use client'

import { useState } from 'react'
import { useMutation } from '@tanstack/react-query'
import { Button } from '@/components/ui/button'
import { useAuthFetcher } from '@/hooks/useAuthFetcher'
import { fetcher } from '@/lib/fetcher'

export function AuthExample() {
  const [result, setResult] = useState<string>('')
  const authFetcher = useAuthFetcher()

  // 方法1: 使用自定义的useAuthFetcher hook
  const handleTestWithAuthFetcher = async () => {
    try {
      setResult('Testing with authFetcher...')
      
      const data = await authFetcher('/v1/phone/signin', {
        method: 'POST',
        needAuth: true,
        body: {
          mobileNumber: '**********',
          password: 'test123'
        }
      })
      
      setResult(`Success: ${JSON.stringify(data, null, 2)}`)
    } catch (error) {
      setResult(`Error: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  // 方法2: 使用React Query mutation（会自动触发全局错误处理）
  const loginMutation = useMutation({
    mutationFn: async (credentials: { mobileNumber: string; password: string }) => {
      return fetcher('/v1/phone/signin', {
        method: 'POST',
        needAuth: true,
        body: credentials
      })
    },
    onSuccess: (data) => {
      setResult(`Mutation Success: ${JSON.stringify(data, null, 2)}`)
    },
    onError: (error) => {
      // 这里的错误处理是局部的，全局错误处理会在AuthQueryProvider中处理
      setResult(`Mutation Error: ${error instanceof Error ? error.message : String(error)}`)
    }
  })

  const handleTestWithMutation = () => {
    setResult('Testing with React Query mutation...')
    loginMutation.mutate({
      mobileNumber: '**********',
      password: 'test123'
    })
  }

  // 方法3: 直接使用fetcher（需要手动处理错误）
  const handleTestWithDirectFetcher = async () => {
    try {
      setResult('Testing with direct fetcher...')
      
      const data = await fetcher('/v1/phone/signin', {
        method: 'POST',
        needAuth: true,
        body: {
          mobileNumber: '**********',
          password: 'test123'
        }
      })
      
      setResult(`Direct Success: ${JSON.stringify(data, null, 2)}`)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      
      // 手动处理认证错误
      if (
        errorMessage === "AUTHENTICATION_REQUIRED" ||
        errorMessage === "AUTHENTICATION_FAILED"
      ) {
        if (typeof window !== 'undefined') {
          localStorage.removeItem('token')
          window.location.href = '/login'
        }
      }
      
      setResult(`Direct Error: ${errorMessage}`)
    }
  }

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">认证错误处理示例</h2>
      
      <div className="space-y-4 mb-6">
        <Button 
          onClick={handleTestWithAuthFetcher}
          variant="outline"
          className="w-full"
        >
          方法1: 使用 useAuthFetcher Hook
        </Button>
        
        <Button 
          onClick={handleTestWithMutation}
          disabled={loginMutation.isPending}
          variant="outline"
          className="w-full"
        >
          方法2: 使用 React Query Mutation
          {loginMutation.isPending && ' (Loading...)'}
        </Button>
        
        <Button 
          onClick={handleTestWithDirectFetcher}
          variant="outline"
          className="w-full"
        >
          方法3: 直接使用 Fetcher
        </Button>
      </div>
      
      {result && (
        <div className="mt-4">
          <h3 className="font-semibold mb-2">结果:</h3>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto whitespace-pre-wrap">
            {result}
          </pre>
        </div>
      )}
      
      <div className="mt-8 p-4 bg-blue-50 rounded">
        <h3 className="font-semibold mb-2">说明:</h3>
        <ul className="list-disc list-inside space-y-1 text-sm">
          <li><strong>方法1 (useAuthFetcher)</strong>: 在自定义hook中处理认证错误和路由跳转</li>
          <li><strong>方法2 (React Query)</strong>: 在AuthQueryProvider中全局处理认证错误</li>
          <li><strong>方法3 (直接fetcher)</strong>: 在每个调用点手动处理认证错误</li>
          <li>推荐使用方法1或方法2，可以统一处理认证逻辑</li>
        </ul>
      </div>
    </div>
  )
}
