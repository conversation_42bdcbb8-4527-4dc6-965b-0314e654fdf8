"use client";

import { useEffect, useState } from "react";
import { useMutation } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { fetcher } from "@/lib/fetcher";

export function AuthTest() {
  const [result, setResult] = useState<string>("");
  const [token, setToken] = useState<string>("");

  // 测试需要认证的请求
  const testAuthMutation = useMutation({
    mutationFn: async () => {
      return fetcher("/v1/phone/signin", {
        method: "POST",
        needAuth: true, // 这会触发认证检查
        body: {
          mobileNumber: "1234567890",
          password: "test123",
          signinType: "password",
        },
      });
    },
    // onSuccess: (data) => {
    //   setResult(`✅ Success: ${JSON.stringify(data, null, 2)}`)
    // },
    // onError: (error) => {
    //   setResult(`❌ Error: ${error instanceof Error ? error.message : String(error)}`)
    // }
  });

  // 测试无token的认证请求
  const testNoTokenMutation = useMutation({
    mutationFn: async () => {
      // 先清除token
      if (typeof window !== "undefined") {
        localStorage.removeItem("token");
      }

      return fetcher("/v1/phone/signin", {
        method: "POST",
        needAuth: true, // 这应该触发 AUTHENTICATION_REQUIRED 错误
        body: {
          mobileNumber: "1234567890",
          password: "test123",
        },
      });
    },
    // onSuccess: (data) => {
    //   setResult(`✅ Success: ${JSON.stringify(data, null, 2)}`)
    // },
    // onError: (error) => {
    //   setResult(`❌ Error: ${error instanceof Error ? error.message : String(error)}`)
    // }
  });

  // 模拟401错误
  const test401Mutation = useMutation({
    mutationFn: async () => {
      // 设置一个无效的token
      if (typeof window !== "undefined") {
        localStorage.setItem("token", "invalid_token_12345");
      }

      return fetcher("/v1/phone/signin", {
        method: "POST",
        needAuth: true,
        body: {
          mobileNumber: "1234567890",
          password: "test123",
        },
      });
    },
    // onSuccess: (data) => {
    //   setResult(`✅ Success: ${JSON.stringify(data, null, 2)}`)
    // },
    // onError: (error) => {
    //   setResult(`❌ Error: ${error instanceof Error ? error.message : String(error)}`)
    // }
  });

  useEffect(() => {
    const storedToken = localStorage.getItem("token") || "无";
    setToken(storedToken);
  }, []);
  const clearResult = () => setResult("");

  return (
    <div className="p-6 max-w-2xl mx-auto bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-4">🔐 认证错误处理测试</h2>

      <div className="space-y-4 mb-6">
        <Button
          onClick={() => {
            clearResult();
            testAuthMutation.mutate();
          }}
          disabled={testAuthMutation.isPending}
          className="w-full"
        >
          测试正常认证请求
          {testAuthMutation.isPending && " (Loading...)"}
        </Button>

        <Button
          onClick={() => {
            clearResult();
            testNoTokenMutation.mutate();
          }}
          disabled={testNoTokenMutation.isPending}
          variant="outline"
          className="w-full"
        >
          测试无Token请求 (应该跳转到登录页)
          {testNoTokenMutation.isPending && " (Loading...)"}
        </Button>

        <Button
          onClick={() => {
            clearResult();
            test401Mutation.mutate();
          }}
          disabled={test401Mutation.isPending}
          variant="outline"
          className="w-full"
        >
          测试无效Token请求 (模拟401错误)
          {test401Mutation.isPending && " (Loading...)"}
        </Button>

        <Button onClick={clearResult} variant="secondary" className="w-full">
          清除结果
        </Button>
      </div>

      {result && (
        <div className="mt-4">
          <h3 className="font-semibold mb-2">测试结果:</h3>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto whitespace-pre-wrap">
            {result}
          </pre>
        </div>
      )}

      <div className="mt-8 p-4 bg-blue-50 rounded">
        <h3 className="font-semibold mb-2">📋 测试说明:</h3>
        <ul className="list-disc list-inside space-y-1 text-sm">
          <li>
            <strong>正常认证请求</strong>: 如果有有效token，应该正常返回数据
          </li>
          <li>
            <strong>无Token请求</strong>: 应该触发 AUTHENTICATION_REQUIRED
            错误并跳转到登录页
          </li>
          <li>
            <strong>无效Token请求</strong>: 应该触发 401 错误并跳转到登录页
          </li>
          <li>所有认证错误都会被 AuthQueryProvider 自动处理</li>
          <li>错误发生时会自动清除本地存储的token并跳转到 /login</li>
        </ul>
      </div>

      <div className="mt-4 p-4 bg-green-50 rounded">
        <h3 className="font-semibold mb-2">✅ 当前Token状态:</h3>
        <div className="text-sm">
          Token: {token ? token || "无" : "检查中..."}
        </div>
      </div>
    </div>
  );
}
