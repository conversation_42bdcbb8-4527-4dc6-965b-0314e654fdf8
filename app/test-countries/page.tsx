"use client";

import { countryCodes, getCountryByDialCode } from "@/lib/country-codes";

export default function TestCountriesPage() {
  // 查找阿富汗
  const afghanistan = countryCodes.find(country => country.code === "AF");
  const afghanistanByDialCode = getCountryByDialCode("93");
  
  // 查找一些其他国家进行对比
  const china = countryCodes.find(country => country.code === "CN");
  const usa = countryCodes.find(country => country.code === "US");

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">🧪 国家代码测试页面</h1>
      
      <div className="space-y-6">
        {/* 阿富汗测试 */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <h2 className="text-xl font-semibold mb-3">🇦🇫 阿富汗 (区号93) 测试</h2>
          
          <div className="space-y-2">
            <div>
              <strong>通过国家代码查找:</strong>
              {afghanistan ? (
                <span className="ml-2 text-green-600">
                  ✅ {afghanistan.flag} {afghanistan.country} (+{afghanistan.dialCode})
                </span>
              ) : (
                <span className="ml-2 text-red-600">❌ 未找到</span>
              )}
            </div>
            
            <div>
              <strong>通过电话区号查找:</strong>
              {afghanistanByDialCode ? (
                <span className="ml-2 text-green-600">
                  ✅ {afghanistanByDialCode.flag} {afghanistanByDialCode.country} ({afghanistanByDialCode.code})
                </span>
              ) : (
                <span className="ml-2 text-red-600">❌ 未找到</span>
              )}
            </div>
          </div>
        </div>

        {/* 其他国家对比 */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h2 className="text-xl font-semibold mb-3">🌍 其他国家对比</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <strong>中国:</strong>
              {china && (
                <span className="ml-2">
                  {china.flag} {china.country} (+{china.dialCode})
                </span>
              )}
            </div>
            
            <div>
              <strong>美国:</strong>
              {usa && (
                <span className="ml-2">
                  {usa.flag} {usa.country} (+{usa.dialCode})
                </span>
              )}
            </div>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="bg-green-50 p-4 rounded-lg">
          <h2 className="text-xl font-semibold mb-3">📊 统计信息</h2>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{countryCodes.length}</div>
              <div className="text-sm text-gray-600">总国家数</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {countryCodes.filter(c => c.dialCode === "93").length}
              </div>
              <div className="text-sm text-gray-600">区号93的国家</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {countryCodes.filter(c => c.code === "AF").length}
              </div>
              <div className="text-sm text-gray-600">代码AF的国家</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {countryCodes.filter(c => c.country.includes("Afghanistan")).length}
              </div>
              <div className="text-sm text-gray-600">名称包含Afghanistan</div>
            </div>
          </div>
        </div>

        {/* 所有区号93的国家 */}
        <div className="bg-yellow-50 p-4 rounded-lg">
          <h2 className="text-xl font-semibold mb-3">📞 所有使用区号93的国家</h2>
          
          <div className="space-y-2">
            {countryCodes
              .filter(country => country.dialCode === "93")
              .map(country => (
                <div key={country.code} className="flex items-center space-x-2">
                  <span className="text-2xl">{country.flag}</span>
                  <span className="font-medium">{country.country}</span>
                  <span className="text-gray-500">({country.code})</span>
                  <span className="text-blue-600">+{country.dialCode}</span>
                </div>
              ))}
          </div>
          
          {countryCodes.filter(country => country.dialCode === "93").length === 0 && (
            <p className="text-red-600">❌ 没有找到使用区号93的国家</p>
          )}
        </div>

        {/* 搜索测试 */}
        <div className="bg-purple-50 p-4 rounded-lg">
          <h2 className="text-xl font-semibold mb-3">🔍 搜索测试</h2>
          
          <div className="space-y-3">
            <div>
              <strong>搜索 "93":</strong>
              <div className="ml-4 mt-1">
                {countryCodes
                  .filter(country => 
                    country.dialCode.includes("93") || 
                    country.country.toLowerCase().includes("93") ||
                    country.code.toLowerCase().includes("93")
                  )
                  .slice(0, 5)
                  .map(country => (
                    <div key={country.code} className="text-sm">
                      {country.flag} {country.country} (+{country.dialCode})
                    </div>
                  ))}
              </div>
            </div>
            
            <div>
              <strong>搜索 "afghanistan":</strong>
              <div className="ml-4 mt-1">
                {countryCodes
                  .filter(country => 
                    country.country.toLowerCase().includes("afghanistan") ||
                    country.code.toLowerCase().includes("af")
                  )
                  .map(country => (
                    <div key={country.code} className="text-sm">
                      {country.flag} {country.country} (+{country.dialCode})
                    </div>
                  ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
