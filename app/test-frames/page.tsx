"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  loadCheckoutScript, 
  initializeFrames, 
  setupFramesEventHandlers, 
  submitCard,
  isFramesLoaded 
} from "@/lib/checkout-frames";

export default function TestFramesPage() {
  const [framesLoading, setFramesLoading] = useState(true);
  const [framesError, setFramesError] = useState<string | null>(null);
  const [cardholderName, setCardholderName] = useState("Test User");
  const [retryCount, setRetryCount] = useState(0);

  const initializePayment = async () => {
    try {
      setFramesLoading(true);
      setFramesError(null);
      
      console.log("开始初始化支付系统...");
      
      // 首先加载 Checkout.com 脚本
      await loadCheckoutScript({ maxRetries: 3 });
      
      // 脚本加载成功后初始化 Frames
      await initializeFrames({
        publicKey: process.env.NEXT_PUBLIC_CHECKOUT_PUBLIC_KEY || "",
        style: {
          base: {
            color: "#000000",
            fontSize: "15px",
            paddingLeft: "12px",
            fontWeight: "600",
          },
          placeholder: {
            base: {
              color: "rgba(0, 0, 0, .3)",
            },
          },
          focus: {
            border: "2px solid #000000",
            borderRadius: "10px",
          },
        },
      });

      // 设置事件监听器
      setupFramesEventHandlers({
        onFrameValidationChanged: (event: any) => {
          console.log("Frame validation changed:", event);
        },
        onCardValidationChanged: (valid: any) => {
          console.log("Card validation changed:", valid);
        },
        onPaymentMethodChanged: (event: any) => {
          console.log("Payment method changed:", event);
        },
        onCardTokenizationFailed: (error: any) => {
          console.error("Card tokenization failed:", error);
          setFramesError("支付处理失败，请重试");
        },
        onCardTokenized: (event: any) => {
          console.log("支付成功，获得 token:", event.token);
          alert(`支付成功！Token: ${event.token}`);
        },
      });
      
      setFramesLoading(false);
      console.log("支付系统初始化成功");
      
    } catch (error) {
      console.error("初始化支付系统失败:", error);
      setFramesLoading(false);
      setFramesError(error instanceof Error ? error.message : "支付系统初始化失败");
      setRetryCount(prev => prev + 1);
    }
  };

  const retryInitialization = () => {
    if (retryCount < 3) {
      initializePayment();
    }
  };

  const handlePayment = () => {
    if (!isFramesLoaded()) {
      setFramesError("支付系统未就绪，请稍后重试");
      return;
    }

    if (framesLoading) {
      console.warn("支付系统正在加载中，请稍候");
      return;
    }

    if (!cardholderName.trim()) {
      setFramesError("请输入持卡人姓名");
      return;
    }

    try {
      submitCard(cardholderName);
    } catch (error) {
      console.error("提交支付信息失败:", error);
      setFramesError(error instanceof Error ? error.message : "提交支付信息失败，请重试");
    }
  };

  useEffect(() => {
    initializePayment();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold text-center mb-6">
          Checkout.com Frames 测试
        </h1>

        {/* 加载状态提示 */}
        {framesLoading && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-center mb-4">
            <div className="text-blue-600 text-sm">
              正在加载支付系统...
            </div>
          </div>
        )}

        {/* 错误状态提示 */}
        {framesError && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
            <div className="text-red-600 text-sm mb-2">
              {framesError}
            </div>
            {retryCount < 3 && (
              <button
                onClick={retryInitialization}
                className="text-red-600 text-sm underline hover:no-underline"
              >
                重试 ({retryCount}/3)
              </button>
            )}
          </div>
        )}

        {/* 成功状态提示 */}
        {!framesLoading && !framesError && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-3 text-center mb-4">
            <div className="text-green-600 text-sm">
              支付系统已就绪
            </div>
          </div>
        )}

        <div className="space-y-4">
          <div>
            <Label className="text-sm text-gray-700">
              持卡人姓名
            </Label>
            <Input
              value={cardholderName}
              onChange={(e) => setCardholderName(e.target.value)}
              className="mt-1 bg-gray-50 border-gray-200"
              placeholder="请输入持卡人姓名"
            />
          </div>

          <div>
            <Label className="text-sm text-gray-700">卡号</Label>
            <div className="card-number-frame h-[40px] mt-1 bg-gray-50 border border-gray-200 rounded-md"></div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label className="text-sm text-gray-700">
                有效期
              </Label>
              <div className="expiry-date-frame h-[40px] mt-1 bg-gray-50 border border-gray-200 rounded-md"></div>
            </div>
            <div>
              <Label className="text-sm text-gray-700">
                安全码
              </Label>
              <div className="cvv-frame h-[40px] mt-1 bg-gray-50 border border-gray-200 rounded-md"></div>
            </div>
          </div>

          <Button
            onClick={handlePayment}
            disabled={framesLoading || !!framesError || !cardholderName.trim()}
            className="w-full mt-6"
          >
            {framesLoading ? "加载中..." : "测试支付"}
          </Button>
        </div>

        <div className="mt-6 text-xs text-gray-500 text-center">
          <p>测试卡号: 4242 4242 4242 4242</p>
          <p>有效期: 任意未来日期</p>
          <p>安全码: 任意3位数字</p>
        </div>
      </div>
    </div>
  );
}
