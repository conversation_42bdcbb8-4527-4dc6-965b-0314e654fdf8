"use client";

import { countryCodes, getCountryByDialCode } from "@/lib/country-codes";

export default function AfghanistanSummaryPage() {
  // 查找阿富汗
  const afghanistan = countryCodes.find(country => country.code === "AF");
  const afghanistanByDialCode = getCountryByDialCode("93");
  
  // 查找其他使用93区号的国家（如果有的话）
  const countries93 = countryCodes.filter(country => country.dialCode === "93");

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="text-center mb-8">
        <h1 className="text-4xl font-bold mb-4">🇦🇫 阿富汗国家信息补充完成</h1>
        <p className="text-lg text-gray-600">
          成功添加了阿富汗（Afghanistan）到国家代码列表中
        </p>
      </div>

      {/* 阿富汗信息卡片 */}
      <div className="bg-gradient-to-r from-green-50 to-blue-50 p-6 rounded-lg mb-6 border-2 border-green-200">
        <div className="flex items-center justify-center mb-4">
          <span className="text-6xl mr-4">🇦🇫</span>
          <div className="text-center">
            <h2 className="text-2xl font-bold text-gray-800">Afghanistan</h2>
            <p className="text-lg text-gray-600">阿富汗</p>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
          <div className="text-center bg-white p-4 rounded-lg shadow">
            <div className="text-sm text-gray-500">国家代码</div>
            <div className="text-xl font-bold text-blue-600">AF</div>
          </div>
          <div className="text-center bg-white p-4 rounded-lg shadow">
            <div className="text-sm text-gray-500">电话区号</div>
            <div className="text-xl font-bold text-green-600">+93</div>
          </div>
          <div className="text-center bg-white p-4 rounded-lg shadow">
            <div className="text-sm text-gray-500">国旗</div>
            <div className="text-2xl">🇦🇫</div>
          </div>
        </div>
      </div>

      {/* 验证结果 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="bg-white p-6 rounded-lg shadow border">
          <h3 className="text-lg font-semibold mb-4 text-gray-800">✅ 验证结果</h3>
          <div className="space-y-3">
            <div className="flex items-center">
              <span className="w-4 h-4 bg-green-500 rounded-full mr-3"></span>
              <span>通过国家代码 "AF" 查找: {afghanistan ? "成功" : "失败"}</span>
            </div>
            <div className="flex items-center">
              <span className="w-4 h-4 bg-green-500 rounded-full mr-3"></span>
              <span>通过电话区号 "93" 查找: {afghanistanByDialCode ? "成功" : "失败"}</span>
            </div>
            <div className="flex items-center">
              <span className="w-4 h-4 bg-green-500 rounded-full mr-3"></span>
              <span>国家信息完整性: {afghanistan?.country && afghanistan?.flag && afghanistan?.dialCode ? "完整" : "不完整"}</span>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <h3 className="text-lg font-semibold mb-4 text-gray-800">📊 统计信息</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span>总国家数量:</span>
              <span className="font-bold text-blue-600">{countryCodes.length}</span>
            </div>
            <div className="flex justify-between">
              <span>使用区号93的国家:</span>
              <span className="font-bold text-green-600">{countries93.length}</span>
            </div>
            <div className="flex justify-between">
              <span>阿富汗在列表中的位置:</span>
              <span className="font-bold text-purple-600">
                {afghanistan ? countryCodes.findIndex(c => c.code === "AF") + 1 : "未找到"}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 使用区号93的所有国家 */}
      <div className="bg-white p-6 rounded-lg shadow border mb-6">
        <h3 className="text-lg font-semibold mb-4 text-gray-800">📞 使用电话区号 +93 的国家</h3>
        {countries93.length > 0 ? (
          <div className="space-y-2">
            {countries93.map((country, index) => (
              <div key={country.code} className="flex items-center p-3 bg-gray-50 rounded-lg">
                <span className="text-2xl mr-3">{country.flag}</span>
                <div className="flex-1">
                  <div className="font-medium">{country.country}</div>
                  <div className="text-sm text-gray-500">代码: {country.code}</div>
                </div>
                <div className="text-blue-600 font-mono">+{country.dialCode}</div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-red-600">❌ 没有找到使用区号93的国家</p>
        )}
      </div>

      {/* 使用示例 */}
      <div className="bg-gray-50 p-6 rounded-lg border">
        <h3 className="text-lg font-semibold mb-4 text-gray-800">💻 使用示例</h3>
        <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm">
          <div className="mb-2">// 导入国家代码库</div>
          <div className="mb-2">import &#123; countryCodes, getCountryByDialCode &#125; from '@/lib/country-codes';</div>
          <div className="mb-4"></div>
          <div className="mb-2">// 通过国家代码查找阿富汗</div>
          <div className="mb-2">const afghanistan = countryCodes.find(country =&gt; country.code === "AF");</div>
          <div className="mb-4"></div>
          <div className="mb-2">// 通过电话区号查找</div>
          <div className="mb-2">const country93 = getCountryByDialCode("93");</div>
          <div className="mb-4"></div>
          <div className="mb-2">// 结果</div>
          <div className="text-yellow-400">console.log(afghanistan); // &#123; code: "AF", country: "Afghanistan", flag: "🇦🇫", dialCode: "93" &#125;</div>
        </div>
      </div>

      {/* 成功提示 */}
      <div className="mt-8 text-center">
        <div className="inline-flex items-center px-6 py-3 bg-green-100 border border-green-300 rounded-lg">
          <span className="text-2xl mr-2">✅</span>
          <span className="text-green-800 font-medium">
            阿富汗（Afghanistan, +93）已成功添加到国家代码列表中！
          </span>
        </div>
      </div>
    </div>
  );
}
