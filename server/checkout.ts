import { fetcher } from "@/lib/fetcher";
import { aesEncrypt } from "@/lib/utils";
import { pickBy } from "lodash-es";

export interface ResponseBase<T> {
  data: T;
  meta: {
    code: number | string;
    message: string;
  };
}

export interface IPhonepassword {
  mobileNumber: string;
  password: string;
  clientId: string;
  code: number;
  countryCode: string | number;
  signinType: string;
  extra: {
    [key: string]: unknown;
  };
}

interface IPhoneCode {
  mobileNumber: string;
  countryCode: string | number;
  clientId: string;
  captcha?: {
    captchaID: string;
    captchaOutput: string;
    genTime: string;
    lotNumber: string;
    passToken: string;
  };
  category: string;
  codeLength: number;
  language: string;
}

interface IEmailCode {
  email: string;
  countryCode: string | number;
  clientId: string;
  captcha: {
    captchaID: string;
    captchaOutput: string;
    genTime: string;
    lotNumber: string;
    passToken: string;
  };
  category: string;
  codeLength: number;
  language: string;
}

interface IEmailPassword {
  email: string;
  password: string;
  clientId: string;
  code: number;
  signinType: string;
  extra: { [key: string]: unknown };
}

interface LoginResponse {
  accessToken: string;
  refreshToken?: string;
  expiresIn?: number;
}

// 购买探币或订阅接口（端外）
export const checkoutOrder = async (data: any): Promise<ResponseBase<any>> => {
  return fetcher<ResponseBase<any>>("/v2/users/me/ttt/checkout-order", {
    method: "POST",
    body: JSON.stringify(data),
    needAuth: false,
  });
};

export const phonePasswordLogin = async (
  data: IPhonepassword
): Promise<ResponseBase<LoginResponse>> => {
  const _data = pickBy(
    {
      ...data,
      password: data?.password ? aesEncrypt(data?.password) : null,
    },
    (value) => value != null && value !== ""
  );

  const response = await fetcher<ResponseBase<LoginResponse>>("/v1/phone/signin", {
    method: "POST",
    body: JSON.stringify(_data),
    needAuth: false,
  });

  return response;
};

// 获取checkout商品信息（端外）
export const checkoutMerchandises = async (): Promise<ResponseBase<any>> => {
  const response = await fetcher<ResponseBase<any>>(
    "/v2/users/me/ttt/checkout-merchandises",
    {
      method: "GET",
      needAuth: false,
    }
  );
  return response;
};

// 获取绑定银行卡信息（端外）
export const checkoutCardsInfo = async (data: any): Promise<ResponseBase<any>> => {
  const response = await fetcher<ResponseBase<any>>("/v2/users/me/ttt/checkout-cards-info", {
    method: "POST",
    body: JSON.stringify(data),
    needAuth: false,
  });

  return response;
};

// 查询checkout订单状态（端外）
export const checkoutOrderStatus = async (data: any): Promise<ResponseBase<any>> => {
  const response = await fetcher<ResponseBase<any>>(
    "/v2/users/me/ttt/checkout-order-status",
    {
      method: "POST",
      body: JSON.stringify(data),
      needAuth: false,
    }
  );
  return response;
};

// 获取checkout合约列表集合（端外）
export const checkoutContractList = async (data: any): Promise<ResponseBase<any>> => {
  const response = await fetcher<ResponseBase<any>>(
    "/v2/users/me/ttt/checkout-contract-list",
    {
      method: "POST",
      body: JSON.stringify(data),
      needAuth: false,
    }
  );
  return response;
};

// checkout取消合约（端外）
export const checkoutContractCancel = async (data: any): Promise<ResponseBase<any>> => {
  const response = await fetcher<ResponseBase<any>>(
    "/v2/users/me/ttt/checkout-contractCancel",
    { method: "POST", body: JSON.stringify(data), needAuth: false }
  );
  return response;
};
