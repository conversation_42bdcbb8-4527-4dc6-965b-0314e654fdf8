export type ThemeKey = "vip" | "see" | "premium" | "ultra";

export const plans = [
  {
    id: "1",
    title: "VIP",
    months: 12,
    monthlyPrice: "$51.5/mo",
    totalPrice: "$618",
    discount: "16%off",
  },
  {
    id: "2",
    title: "SEE",
    months: 3,
    monthlyPrice: "$32.5/mo",
    totalPrice: "$128",
  },
  {
    id: "3",
    title: "PREMIUM",
    months: 3,
    monthlyPrice: "$32.5/mo",
    totalPrice: "$128",
  },
];

export const shopList = [
  {
    type: "vip",
    fold: false,
    items: [
      {
        id: 5205,
        payment: "checkout",
        category: "tttWebVip",
        quantity: 1,
        productType: "auto-renewable",
        productName: "vip",
        defaultStockKeepUnit: {
          id: 5205,
          prices: {
            currencySymbol: "US$",
            currencyCode: "USD",
            price: "8.99",
            unitPrice: "8.99",
            originalPrice: "0.00",
            originalUnitPrice: "8.99",
            noneRenewalPrice: "0.00",
          },
          extraDisplayOption: {},
        },
        name: "vip",
        categoryName: "vip",
      },
      {
        id: 5206,
        payment: "checkout",
        category: "tttWebVip",
        quantity: 3,
        productType: "auto-renewable",
        productName: "vip",
        defaultStockKeepUnit: {
          id: 5206,
          prices: {
            currencySymbol: "US$",
            currencyCode: "USD",
            price: "16.99",
            unitPrice: "8.99",
            originalPrice: "0.00",
            originalUnitPrice: "8.99",
            noneRenewalPrice: "0.00",
          },
          extraDisplayOption: {},
        },
        name: "vip",
        categoryName: "vip",
      },
      {
        id: 5207,
        payment: "checkout",
        category: "tttWebVip",
        quantity: 12,
        productType: "auto-renewable",
        productName: "vip",
        defaultStockKeepUnit: {
          id: 5207,
          prices: {
            currencySymbol: "US$",
            currencyCode: "USD",
            price: "54.99",
            unitPrice: "8.99",
            originalPrice: "0.00",
            originalUnitPrice: "8.99",
            noneRenewalPrice: "0.00",
          },
          extraDisplayOption: {},
        },
        name: "vip",
        categoryName: "vip",
      },
    ],
  },
  {
    type: "see",
    fold: false,
    items: [
      {
        id: 5208,
        payment: "checkout",
        category: "tttWebSee",
        quantity: 1,
        productType: "auto-renewable",
        productName: "seeWhoLikedMe",
        defaultStockKeepUnit: {
          id: 5208,
          prices: {
            currencySymbol: "US$",
            currencyCode: "USD",
            price: "12.99",
            unitPrice: "12.99",
            originalPrice: "0.00",
            originalUnitPrice: "12.99",
            noneRenewalPrice: "0.00",
          },
          extraDisplayOption: {},
        },
        name: "see",
        categoryName: "see",
      },
      {
        id: 5209,
        payment: "checkout",
        category: "tttWebSee",
        quantity: 3,
        productType: "auto-renewable",
        productName: "seeWhoLikedMe",
        defaultStockKeepUnit: {
          id: 5209,
          prices: {
            currencySymbol: "US$",
            currencyCode: "USD",
            price: "24.99",
            unitPrice: "12.99",
            originalPrice: "0.00",
            originalUnitPrice: "12.99",
            noneRenewalPrice: "0.00",
          },
          extraDisplayOption: {},
        },
        name: "see",
        categoryName: "see",
      },
      {
        id: 5210,
        payment: "checkout",
        category: "tttWebSee",
        quantity: 12,
        productType: "auto-renewable",
        productName: "seeWhoLikedMe",
        defaultStockKeepUnit: {
          id: 5210,
          prices: {
            currencySymbol: "US$",
            currencyCode: "USD",
            price: "69.99",
            unitPrice: "12.99",
            originalPrice: "0.00",
            originalUnitPrice: "12.99",
            noneRenewalPrice: "0.00",
          },
          extraDisplayOption: {},
        },
        name: "see",
        categoryName: "see",
      },
    ],
  },
  {
    type: "premium",
    fold: false,
    items: [
      {
        id: 5211,
        payment: "checkout",
        category: "tttWebPremium",
        quantity: 1,
        productType: "auto-renewable",
        productName: "svip",
        defaultStockKeepUnit: {
          id: 5211,
          prices: {
            currencySymbol: "US$",
            currencyCode: "USD",
            price: "16.99",
            unitPrice: "16.99",
            originalPrice: "0.00",
            originalUnitPrice: "16.99",
            noneRenewalPrice: "0.00",
          },
          extraDisplayOption: {},
        },
        name: "premium",
        categoryName: "premium",
      },
      {
        id: 5212,
        payment: "checkout",
        category: "tttWebPremium",
        quantity: 3,
        productType: "auto-renewable",
        productName: "svip",
        defaultStockKeepUnit: {
          id: 5212,
          prices: {
            currencySymbol: "US$",
            currencyCode: "USD",
            price: "32.99",
            unitPrice: "16.99",
            originalPrice: "0.00",
            originalUnitPrice: "16.99",
            noneRenewalPrice: "0.00",
          },
          extraDisplayOption: {},
        },
        name: "premium",
        categoryName: "premium",
      },
      {
        id: 5213,
        payment: "checkout",
        category: "tttWebPremium",
        quantity: 12,
        productType: "auto-renewable",
        productName: "svip",
        defaultStockKeepUnit: {
          id: 5213,
          prices: {
            currencySymbol: "US$",
            currencyCode: "USD",
            price: "89.99",
            unitPrice: "16.99",
            originalPrice: "0.00",
            originalUnitPrice: "16.99",
            noneRenewalPrice: "0.00",
          },
          extraDisplayOption: {},
        },
        name: "premium",
        categoryName: "premium",
      },
    ],
  },
  {
    type: "ultra",
    fold: false,
    items: [
      {
        id: 5214,
        payment: "checkout",
        category: "tttWebUltraPremium",
        quantity: 1,
        productType: "auto-renewable",
        productName: "ultraPremium",
        defaultStockKeepUnit: {
          id: 5214,
          prices: {
            currencySymbol: "US$",
            currencyCode: "USD",
            price: "103.99",
            unitPrice: "103.99",
            originalPrice: "0.00",
            originalUnitPrice: "103.99",
            noneRenewalPrice: "0.00",
          },
          extraDisplayOption: {},
        },
        name: "ultra",
        categoryName: "ultra",
      },
      {
        id: 5215,
        payment: "checkout",
        category: "tttWebUltraPremium",
        quantity: 3,
        productType: "auto-renewable",
        productName: "ultraPremium",
        defaultStockKeepUnit: {
          id: 5215,
          prices: {
            currencySymbol: "US$",
            currencyCode: "USD",
            price: "259.99",
            unitPrice: "103.99",
            originalPrice: "0.00",
            originalUnitPrice: "103.99",
            noneRenewalPrice: "0.00",
          },
          extraDisplayOption: {},
        },
        name: "ultra",
        categoryName: "ultra",
      },
      {
        id: 5216,
        payment: "checkout",
        category: "tttWebUltraPremium",
        quantity: 12,
        productType: "auto-renewable",
        productName: "ultraPremium",
        defaultStockKeepUnit: {
          id: 5216,
          prices: {
            currencySymbol: "US$",
            currencyCode: "USD",
            price: "759.99",
            unitPrice: "103.99",
            originalPrice: "0.00",
            originalUnitPrice: "103.99",
            noneRenewalPrice: "0.00",
          },
          extraDisplayOption: {},
        },
        name: "ultra",
        categoryName: "ultra",
      },
    ],
  },
];

export const tabs = [
  { id: "vip", label: "VIP" },
  { id: "see", label: "SEE" },
  { id: "premium", label: "PREMIUM" },
  { id: "ultra", label: "ULTRA PREMIUM" },
];

export const themeStyles = {
  vip: {
    background: "bg-gradient-to-b from-[#FFEFC0] via-[#FFF3D0] to-[#FFFAED]", //充值页面根容器背景
    cardBackground: "bg-vip-background", //选中订阅的plan计划背景
    text: "text-vip-text", //选中订阅的plan计划文字颜色
    planPriceText: "text-[#DFB43A]", //选中订阅的plan每月价格
    priceTextSelected: "text-vip-text", //选中订阅的plan计划合计价格文字颜色
    priceTextUnSelected: "text-vip-text", //未选中订阅的plan计划价格文字颜色
    textSecondary: "text-vip-textSecondary", // 订阅过期富标题文案颜色
    tabSelected: "bg-vip-primary text-vip-text", //选中订阅的plan计划文字颜色
    tabUnselected: "", //未选中订阅的plan计划文字颜色
    planSelectText: "text-white", //选中订阅的plan计划文字颜色
    planUnSelectText: "text-black", //未选中订阅的plan计划文字颜色
    selectedCard: "bg-vip-primary", //选中订阅的plan计划背景颜色
    unselectedCard: "bg-white border-gray-300", //未选中订阅的plan计划背景颜色
    selectedCardBorder: "border-vip-primary", //选中订阅的plan计划边框颜色
    unSelectedCardBorder: "border-vip-background", //未选中订阅的plan计划边框颜色
    icon: "👑",
    iconBg: "bg-gradient-to-br from-yellow-400 to-orange-500",
    button: "bg-gradient-to-r from-yellow-400 to-yellow-500 text-gray-800",
    headerBg: "bg-yellow-100/80",
    paymentBg: "bg-white",
    featuresTableBg: "bg-white",
    featuresHeaderBg: "bg-yellow-400",
    features: [
      { name: "Unlimited Likes", desc: "Like Freely Without Worry" },
      { name: "5 Free Super Like per Day", desc: "5x Match Success Rate" },
    ],
    specialFeature: null,
  },
  see: {
    background: "bg-gradient-to-b from-[#FFE2BF] via-[#FFF4E8] to-[#FFFBF7]",
    cardBackground: "bg-see-background",
    text: "text-see-text",
    planPriceText: "text-[#FF932B]", //选中订阅的plan每月价格
    priceTextSelected: "text-white",
    priceTextUnSelected: "text-see-text",
    textSecondary: "text-see-textSecondary",
    tabSelected: "bg-see-primary text-white ]",
    tabUnselected: "",
    planSelectText: "text-white",
    planUnSelectText: "text-black",
    selectedCard: "bg-see-primary text-white",
    selectedCardBorder: "border-see-primary",
    unselectedCard: "bg-white border-gray-300",
    unSelectedCardBorder: "border-see-border",
    icon: "💎",
    iconBg: "bg-gradient-to-br from-orange-400 to-red-500",
    button: "bg-gradient-to-r from-orange-400 to-orange-500 text-white",
    headerBg: "bg-orange-100/80",
    paymentBg: "bg-white",
    featuresTableBg: "bg-white",
    featuresHeaderBg: "bg-orange-500",
    features: [
      { name: "Unlimited Likes", desc: "Like Freely Without Worry" },
      { name: "5 Free Super Like per Day", desc: "5x Match Success Rate" },
    ],
    specialFeature: {
      title: "See Who Liked You",
      desc: "Respond to those who liked you instantly",
      avatars: [
        "/placeholder.svg?height=60&width=60",
        "/placeholder.svg?height=60&width=60",
        "/placeholder.svg?height=60&width=60",
      ],
    },
  },
  premium: {
    background: "bg-gradient-to-b from-[#493f31]  to-[#141211]",
    cardBackground: "bg-premium-background",
    text: "text-premium-text",
    planPriceText: "text-vip-text", //选中订阅的plan每月价格

    priceTextSelected: "text-black",
    priceTextUnSelected: "text-premium-text",
    textSecondary: "text-premium-textSecondary",
    tabSelected:
      "bg-[linear-gradient(-45deg,#fed998_0%,#fdecb9_50%,#fed998_100%)] text-black",
    tabUnselected: "bg-premium-tabBgColor text-gray-400",
    planSelectText: "text-black",
    planUnSelectText: "text-white",
    selectedCard:
      "bg-[linear-gradient(-45deg,#fed998_0%,#fdecb9_50%,#fed998_100%)] text-black",
    selectedCardBorder: "border-premium-primary",
    unSelectedCardBorder: "border-premium-border",
    unselectedCard: "bg-gray-800 border-gray-600",
    icon: "💎",
    iconBg: "bg-gradient-to-br from-yellow-400 to-orange-500",
    button: "bg-gradient-to-r from-yellow-400 to-yellow-500 text-black",
    headerBg: "",
    paymentBg: "bg-white",
    featuresTableBg: "bg-gray-800",
    featuresHeaderBg: "bg-yellow-600",
    features: [
      { name: "Unlimited Likes", desc: "Like Freely Without Worry" },
      { name: "5 Free Super Like per Day", desc: "5x Match Success Rate" },
    ],
    specialFeature: null,
  },
  ultra: {
    background: "bg-[#120F10]",
    cardBackground: "",
    text: "text-ultra-text",
    planPriceText: "text-vip-text", //选中订阅的plan每月价格
    priceTextSelected: "",
    priceTextUnSelected: "text-ultra-text",
    textSecondary: "text-ultra-textSecondary",
    tabSelected: "bg-ultra-primary text-black",
    tabUnselected: "bg-ultra-tabBgColor text-gray-400",
    selectedCard: "bg-ultra-primary text-black",
    planSelectText: "text-black",
    planUnSelectText: "text-white",
    selectedCardBorder: "border-ultra-primary",
    unSelectedCardBorder: "border-ultra-border",
    unselectedCard: "bg-ultra-card",
    icon: "💎",
    iconBg: "bg-gradient-to-br from-purple-500 to-pink-500",
    button: "bg-gradient-to-r from-purple-500 to-purple-600 text-white",
    headerBg: "",
    paymentBg: "bg-white",
    featuresTableBg: "bg-gray-800",
    featuresHeaderBg: "bg-gradient-to-r from-purple-500 to-pink-500",
    features: [
      { name: "Unlimited Likes", desc: "Like Freely Without Worry" },
      { name: "5 Free Super Like per Day", desc: "5x Match Success Rate" },
    ],
    specialFeature: null,
  },
};

export const mobileThemeStyles = {
  vip: {
    background: "bg-gradient-to-b from-[#FFE391] to-[#FFFFFF]", //充值页面根容器背景
    cardBackground: "bg-vip-background", //选中订阅的plan计划背景
    text: "text-vip-text", //选中订阅的plan计划文字颜色
    planPriceText: "text-[#DFB43A]", //选中订阅的plan每月价格
    priceTextSelected: "text-vip-text", //选中订阅的plan计划合计价格文字颜色
    priceTextUnSelected: "text-vip-text", //未选中订阅的plan计划价格文字颜色
    textSecondary: "text-vip-textSecondary", // 订阅过期富标题文案颜色
    tabSelected: "bg-vip-primary text-vip-text", //选中订阅的plan计划文字颜色
    tabUnselected: "", //未选中订阅的plan计划文字颜色
    planSelectText: "text-gray-800", //选中订阅的plan计划文字颜色
    planUnSelectText: "text-black", //未选中订阅的plan计划文字颜色
    selectedCard: "bg-vip-primary", //选中订阅的plan计划背景颜色
    unselectedCard: "bg-white border-gray-300", //未选中订阅的plan计划背景颜色
    selectedCardBorder: "border-vip-primary", //选中订阅的plan计划边框颜色
    unSelectedCardBorder: "border-vip-background", //未选中订阅的plan计划边框颜色
    bg: "bg-[url('https://oversea.hellogroupcdn.com/s1/u/fbbfedcfd/vip_mobile_bg.png')] bg-[length:100%_100%]",
  },
  see: {
    background: "bg-gradient-to-b from-[#FFEFC0] via-[#FFF3D0] to-[#FFFAED]", //充值页面根容器背景
    cardBackground: "bg-vip-background", //选中订阅的plan计划背景
    text: "text-vip-text", //选中订阅的plan计划文字颜色
    planPriceText: "text-[#DFB43A]", //选中订阅的plan每月价格
    priceTextSelected: "text-vip-text", //选中订阅的plan计划合计价格文字颜色
    priceTextUnSelected: "text-vip-text", //未选中订阅的plan计划价格文字颜色
    textSecondary: "text-vip-textSecondary", // 订阅过期富标题文案颜色
    tabSelected: "bg-vip-primary text-vip-text", //选中订阅的plan计划文字颜色
    tabUnselected: "", //未选中订阅的plan计划文字颜色
    planSelectText: "text-gray-800", //选中订阅的plan计划文字颜色
    planUnSelectText: "text-black", //未选中订阅的plan计划文字颜色
    selectedCard: "bg-vip-primary", //选中订阅的plan计划背景颜色
    unselectedCard: "bg-white border-gray-300", //未选中订阅的plan计划背景颜色
    selectedCardBorder: "border-vip-primary", //选中订阅的plan计划边框颜色
    unSelectedCardBorder: "border-vip-background", //未选中订阅的plan计划边框颜色
    bg: "https://s.momocdn.com/s1/u/fbbfedcfd/see_mobile_bg.png",
  },
  premium: {
    background: "bg-gradient-to-b from-[#FFEFC0] via-[#FFF3D0] to-[#FFFAED]", //充值页面根容器背景
    cardBackground: "bg-vip-background", //选中订阅的plan计划背景
    text: "text-vip-text", //选中订阅的plan计划文字颜色
    planPriceText: "text-[#DFB43A]", //选中订阅的plan每月价格
    priceTextSelected: "text-vip-text", //选中订阅的plan计划合计价格文字颜色
    priceTextUnSelected: "text-vip-text", //未选中订阅的plan计划价格文字颜色
    textSecondary: "text-vip-textSecondary", // 订阅过期富标题文案颜色
    tabSelected: "bg-vip-primary text-vip-text", //选中订阅的plan计划文字颜色
    tabUnselected: "", //未选中订阅的plan计划文字颜色
    planSelectText: "text-gray-800", //选中订阅的plan计划文字颜色
    planUnSelectText: "text-black", //未选中订阅的plan计划文字颜色
    selectedCard: "bg-vip-primary", //选中订阅的plan计划背景颜色
    unselectedCard: "bg-white border-gray-300", //未选中订阅的plan计划背景颜色
    selectedCardBorder: "border-vip-primary", //选中订阅的plan计划边框颜色
    unSelectedCardBorder: "border-vip-background", //未选中订阅的plan计划边框颜色
    bg: "https://s.momocdn.com/s1/u/fbbfedcfd/premium_mobile_bg.png",
  },
  ultra: {
    background: "bg-gradient-to-b from-[#FFEFC0] via-[#FFF3D0] to-[#FFFAED]", //充值页面根容器背景
    cardBackground: "bg-vip-background", //选中订阅的plan计划背景
    text: "text-vip-text", //选中订阅的plan计划文字颜色
    planPriceText: "text-[#DFB43A]", //选中订阅的plan每月价格
    priceTextSelected: "text-vip-text", //选中订阅的plan计划合计价格文字颜色
    priceTextUnSelected: "text-vip-text", //未选中订阅的plan计划价格文字颜色
    textSecondary: "text-vip-textSecondary", // 订阅过期富标题文案颜色
    tabSelected: "bg-vip-primary text-vip-text", //选中订阅的plan计划文字颜色
    tabUnselected: "", //未选中订阅的plan计划文字颜色
    planSelectText: "text-white", //选中订阅的plan计划文字颜色
    planUnSelectText: "text-black", //未选中订阅的plan计划文字颜色
    selectedCard: "bg-vip-primary", //选中订阅的plan计划背景颜色
    unselectedCard: "bg-white border-gray-300", //未选中订阅的plan计划背景颜色
    selectedCardBorder: "border-vip-primary", //选中订阅的plan计划边框颜色
    unSelectedCardBorder: "border-vip-background", //未选中订阅的plan计划边框颜色
    bg: "https://s.momocdn.com/s1/u/fbbfedcfd/ultra_mobile_bg.png",
  },
};
