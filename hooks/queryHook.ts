import { useMutation } from "@tanstack/react-query";
import { emailCodeSend, emailSign, phoneCodeSend, phonePasswordLogin } from "@/server/login";
import {
  checkoutOrder,
  checkoutMerchandises,
  checkoutCardsInfo,
  checkoutOrderStatus,
  checkoutContractList,
  checkoutContractCancel,
} from "@/server/checkout";

// 注意：QueryClient 现在在 AuthQueryProvider 中创建和配置
// 这样可以访问 useRouter hook 进行路由跳转
export const usePhonePasswordLogin = () => {
  return useMutation({
    mutationFn: phonePasswordLogin,
    onSuccess: (data) => {},
    onError: (error) => {},
  });
};
export const usePhoneCodeSend = () => {
  return useMutation({
    mutationFn: phoneCodeSend,
    onSuccess: (data) => {},
    onError: (error) => {},
  });
};
export const useEmailCodeSend = () => {
  return useMutation({
    mutationFn: emailCodeSend,
    onSuccess: (data) => {},
    onError: (error) => {},
  });
};

export const useEmailSign = () => {
  return useMutation({
    mutationFn: emailSign,
    onSuccess: (data) => {      
    },
    onError: (error) => {},
  });
};




export const useCheckoutOrder = () => {
  return useMutation({
    mutationFn: emailCodeSend,
    onSuccess: (data) => {},
    onError: (error) => {},
  });
};

export const useCheckoutMerchandises = () => {
  return useMutation({
    mutationFn: checkoutMerchandises,
    onSuccess: (data) => {},
    onError: (error) => {},
  });
};
export const useCheckoutCardsInfo = () => {
  return useMutation({
    mutationFn: checkoutCardsInfo,
    onSuccess: (data) => {},
    onError: (error) => {},
  });
};
export const useCheckoutOrderStatus = () => {
  return useMutation({
    mutationFn: checkoutOrderStatus,
    onSuccess: (data) => {},
    onError: (error) => {},
  });
};
export const useCheckoutContractList = () => {
  return useMutation({
    mutationFn: checkoutContractList,
    onSuccess: (data) => {},
    onError: (error) => {},
  });
};
export const useCheckoutContractCancel = () => {
  return useMutation({
    mutationFn: checkoutContractCancel,
    onSuccess: (data) => {},
    onError: (error) => {},
  });
};


