# Checkout.com Frames 加载问题修复

## 问题描述

Checkout.com Frames 有时候无法加载成功，导致无法输入信用卡信息，影响用户支付体验。

## 根本原因分析

1. **脚本加载时机问题**：直接在 `useEffect` 中调用 `initFrames()`，没有确保脚本已经加载完成
2. **缺少错误处理**：没有对脚本加载失败的情况进行处理
3. **缺少重试机制**：网络问题导致脚本加载失败时，没有重试机制
4. **初始化时机不当**：可能在脚本还未完全加载时就尝试初始化 Frames

## 解决方案

### 1. 创建专门的 Checkout.com 工具模块

创建了 `lib/checkout-frames.ts` 文件，提供以下功能：

- `loadCheckoutScript()`: 带重试机制的脚本加载
- `initializeFrames()`: 安全的 Frames 初始化
- `setupFramesEventHandlers()`: 统一的事件处理器设置
- `submitCard()`: 安全的支付提交
- `isFramesLoaded()`: 检查 Frames 是否已加载

### 2. 改进脚本加载机制

```typescript
// 带重试机制的脚本加载
export const loadCheckoutScript = async (options: FramesLoadOptions = {}): Promise<void> => {
  const { maxRetries = 3 } = options;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      await loadCheckoutScriptOnce(options);
      return; // 成功加载，退出重试循环
    } catch (error) {
      console.error(`第 ${attempt} 次加载 Checkout.com 脚本失败:`, error);
      
      if (attempt === maxRetries) {
        throw new Error(`经过 ${maxRetries} 次尝试后，Checkout.com 脚本加载失败`);
      }
      
      // 等待一段时间后重试
      await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
    }
  }
};
```

### 3. 添加超时和错误处理

```typescript
// 设置超时机制
const timeoutId = setTimeout(() => {
  script.remove();
  reject(new Error(`Checkout.com 脚本加载超时 (${timeout}ms)`));
}, timeout);

script.onload = () => {
  clearTimeout(timeoutId);
  console.log("Checkout.com 脚本加载成功");
  
  // 等待一小段时间确保 Frames 对象完全初始化
  setTimeout(() => {
    if (isFramesLoaded()) {
      resolve();
    } else {
      reject(new Error("Checkout.com Frames 对象未找到"));
    }
  }, 200);
};
```

### 4. 改进初始化流程

```typescript
const initializePayment = async () => {
  try {
    setFramesLoading(true);
    setFramesError(null);
    
    // 首先加载 Checkout.com 脚本
    await loadCheckoutScript({ maxRetries: 3 });
    
    // 脚本加载成功后初始化 Frames
    await initializeFrames({
      publicKey: process.env.NEXT_PUBLIC_CHECKOUT_PUBLIC_KEY || "",
      // ... 样式配置
    });
    
    // 设置事件监听器
    setupFramesEventHandlers({
      // ... 事件处理器
    });
    
    setFramesLoading(false);
    console.log("支付系统初始化成功");
    
  } catch (error) {
    console.error("初始化支付系统失败:", error);
    setFramesLoading(false);
    setFramesError(error instanceof Error ? error.message : "支付系统初始化失败");
    setRetryCount(prev => prev + 1);
  }
};
```

### 5. 添加用户界面状态管理

```typescript
// 状态管理
const [framesLoading, setFramesLoading] = useState(true);
const [framesError, setFramesError] = useState<string | null>(null);
const [retryCount, setRetryCount] = useState(0);

// 重试机制
const retryInitialization = () => {
  if (retryCount < 3) {
    setFramesError(null);
    initializePayment();
  }
};
```

### 6. 改进用户体验

- 添加加载状态提示
- 显示错误信息和重试按钮
- 禁用支付按钮直到系统就绪
- 提供测试页面用于验证修复效果

## 测试

创建了测试页面 `/test-frames` 来验证修复效果：

```bash
# 访问测试页面
http://localhost:3000/test-frames
```

测试页面包含：
- 加载状态显示
- 错误处理和重试机制
- 完整的支付表单
- 测试卡号信息

## 使用方法

### 在现有组件中使用

```typescript
import { 
  loadCheckoutScript, 
  initializeFrames, 
  setupFramesEventHandlers, 
  submitCard,
  isFramesLoaded 
} from "@/lib/checkout-frames";

// 初始化
useEffect(() => {
  const init = async () => {
    try {
      await loadCheckoutScript();
      await initializeFrames({ publicKey: "your-public-key" });
      setupFramesEventHandlers({
        onCardTokenized: (event) => {
          console.log("Token:", event.token);
        }
      });
    } catch (error) {
      console.error("初始化失败:", error);
    }
  };
  init();
}, []);

// 提交支付
const handlePayment = () => {
  if (isFramesLoaded()) {
    submitCard(cardholderName);
  }
};
```

## 环境变量

确保设置了正确的环境变量：

```env
NEXT_PUBLIC_CHECKOUT_PUBLIC_KEY=pk_sbox_mlzqsrpacupf3rlwzn6jzx5bxyu
```

## 主要改进点

1. ✅ **可靠的脚本加载**：带重试机制和超时处理
2. ✅ **错误处理**：完善的错误捕获和用户提示
3. ✅ **状态管理**：清晰的加载、错误、重试状态
4. ✅ **用户体验**：加载提示、错误信息、重试按钮
5. ✅ **代码组织**：模块化的工具函数
6. ✅ **测试支持**：专门的测试页面

## 注意事项

1. 确保网络连接稳定
2. 检查 CDN 地址是否可访问
3. 验证环境变量配置正确
4. 在生产环境中测试完整流程

这些修复应该能够显著提高 Checkout.com Frames 的加载成功率和用户体验。
