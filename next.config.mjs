/** @type {import('next').NextConfig} */
const nextConfig = {
  async rewrites() {
    return [
      {
        source: "/v1/:path*", // 你前端请求的路径
        destination: "http://web-api.staging2.p1staff.com/v1/:path*", // 目标后端地址
      },
      {
        source: "/v2/:path*", // 你前端请求的路径
        destination: "http://web-api.staging2.p1staff.com/v2/:path*", // 目标后端地址
      },
      // 你可以添加更多规则
    ];
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
};

export default nextConfig;
